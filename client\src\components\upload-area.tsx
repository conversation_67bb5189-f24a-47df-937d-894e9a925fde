import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useTranslation } from 'react-i18next';
import { Upload, Image, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useWatermarkRemoval } from '@/hooks/use-watermark-removal';
import type { ProcessingImageItem } from '@/types/api';

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const ACCEPTED_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp'];

export const UploadArea = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { processingImages, addFiles, removeImage, retryImage, clearAll } = useWatermarkRemoval();
  const [dragActive, setDragActive] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setDragActive(false);

    // Handle rejected files
    rejectedFiles.forEach((rejection) => {
      const { file, errors } = rejection;
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          toast({
            title: t('errors.fileTooBig'),
            description: `${file.name} - ${t('upload.maxSize')}`,
            variant: 'destructive',
          });
        } else if (error.code === 'file-invalid-type') {
          toast({
            title: t('errors.invalidFormat'),
            description: `${file.name} - ${t('upload.supportedFormats')}`,
            variant: 'destructive',
          });
        }
      });
    });

    // Handle accepted files
    if (acceptedFiles.length > 0) {
      addFiles(acceptedFiles);
    }
  }, [addFiles, t, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp']
    },
    maxSize: MAX_FILE_SIZE,
    multiple: true,
  });

  const downloadImage = useCallback((url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  return (
    <div className="max-w-4xl mx-auto">
      {/* Upload Area */}
      <div 
        {...getRootProps()} 
        className={`relative glass-card rounded-3xl shadow-2xl border-2 border-dashed transition-all duration-300 p-12 text-center group cursor-pointer interactive-hover ${
          isDragActive || dragActive 
            ? 'border-primary bg-primary/10 scale-105' 
            : 'border-gray-300 hover:border-primary'
        }`}
      >
        <input {...getInputProps()} />
        
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        <div className="relative z-10">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center animate-float">
            <Upload className="w-12 h-12 text-white" />
          </div>
          
          <h3 className="text-2xl font-semibold text-gray-900 mb-4 hero-title">
            {dragActive ? t('upload.dragActive') : t('upload.title')}
          </h3>
          <p className="text-gray-600 mb-6 hero-subtitle">{t('upload.subtitle')}</p>
          
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500 mb-8">
            <span className="px-3 py-1 bg-gray-100 rounded-full">JPG</span>
            <span className="px-3 py-1 bg-gray-100 rounded-full">JPEG</span>
            <span className="px-3 py-1 bg-gray-100 rounded-full">PNG</span>
            <span className="px-3 py-1 bg-gray-100 rounded-full">BMP</span>
          </div>
          
          <p className="text-sm text-gray-500 mb-6">{t('upload.maxSize')}</p>
          
          <Button
            className="gradient-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 btn-primary"
            onClick={(e) => {
              e.stopPropagation();
              // Trigger file input click
              const input = document.querySelector('input[type="file"]') as HTMLInputElement;
              if (input) {
                input.click();
              }
            }}
          >
            {t('upload.button')}
          </Button>
        </div>
      </div>
      
      {/* Processing Area */}
      {processingImages.length > 0 && (
        <div className="mt-8 bg-white rounded-2xl shadow-xl p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-8">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                <Image className="w-8 h-8 text-white processing-pulse" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('processing.title')}</h3>
              <p className="text-gray-600">{t('processing.subtitle')}</p>
            </div>
            <Button 
              onClick={clearAll}
              variant="outline"
              className="ml-4"
            >
              Clear All
            </Button>
          </div>
          
          <div className="space-y-4">
            {processingImages.map((image) => (
              <ProcessingImageItem 
                key={image.id} 
                image={image} 
                onRemove={removeImage}
                onRetry={retryImage}
                onDownload={downloadImage}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface ProcessingImageItemProps {
  image: ProcessingImageItem;
  onRemove: (id: string) => void;
  onRetry: (id: string) => void;
  onDownload: (url: string, filename: string) => void;
}

const ProcessingImageItem = ({ image, onRemove, onRetry, onDownload }: ProcessingImageItemProps) => {
  const { t } = useTranslation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'processing': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'failed':
        return (
          <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <AlertCircle className="w-2 h-2 text-white" />
          </div>
        );
      case 'processing':
        return (
          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
        );
      default:
        return (
          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
        );
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
      {/* Image Preview Section */}
      <div className="flex flex-col lg:flex-row gap-6 mb-4">
        {/* Original Image */}
        <div className="flex-1">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Original</h4>
          <div className="relative">
            <img
              src={image.preview}
              alt={image.file.name}
              className="w-full h-48 object-cover rounded-lg border border-gray-200"
            />
            <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
              Original
            </div>
          </div>
        </div>

        {/* Processed Image */}
        <div className="flex-1">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Processed</h4>
          <div className="relative">
            {image.status === 'completed' && image.processedImageUrl ? (
              <>
                <img
                  src={image.processedImageUrl}
                  alt={`Processed ${image.file.name}`}
                  className="w-full h-48 object-cover rounded-lg border border-gray-200"
                />
                <div className="absolute top-2 left-2 bg-green-500 bg-opacity-90 text-white px-2 py-1 rounded text-xs">
                  Watermark Removed
                </div>
              </>
            ) : (
              <div className="w-full h-48 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                {image.status === 'processing' && (
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-gray-500">Processing...</p>
                  </div>
                )}
                {image.status === 'pending' && (
                  <p className="text-sm text-gray-500">Waiting to process...</p>
                )}
                {image.status === 'failed' && (
                  <div className="text-center">
                    <div className="text-red-500 mb-2">⚠️</div>
                    <p className="text-sm text-red-500">Processing failed</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* File Info and Actions */}
      <div className="flex items-center justify-between">
        <div>
          <p className="font-medium text-gray-900">{image.file.name}</p>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>{(image.file.size / 1024 / 1024).toFixed(2)} MB</span>
            {image.processingTime && (
              <span>{(image.processingTime / 1000).toFixed(2)}s</span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            {getStatusIcon(image.status)}
            <span className={`text-sm ${getStatusColor(image.status)}`}>
              {t(`processing.status.${image.status}`)}
            </span>
          </div>

          {image.error && (
            <Alert className="max-w-xs">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {image.error}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex items-center space-x-2">
            {image.status === 'completed' && image.processedImageUrl && (
              <Button
                onClick={() => onDownload(image.processedImageUrl!, `processed_${image.file.name}`)}
                className="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-primary/90 transition-colors btn-primary"
              >
                {t('processing.actions.download')}
              </Button>
            )}

            {image.status === 'failed' && (
              <Button
                onClick={() => onRetry(image.id)}
                className="bg-orange-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-orange-600 transition-colors"
              >
                {t('processing.actions.retry')}
              </Button>
            )}

            <Button
              onClick={() => onRemove(image.id)}
              variant="outline"
              size="sm"
              className="text-gray-500 hover:text-red-500 px-3 py-1"
            >
              Remove
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
