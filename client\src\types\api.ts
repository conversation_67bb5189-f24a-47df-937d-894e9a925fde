export interface ProcessingImageItem {
  id: string;
  file: File;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
  processedImageUrl?: string;
  processingTime?: number;
  preview: string;
}

export interface WatermarkRemovalResponse {
  id: number;
  success: boolean;
  processedImageUrl?: string;
  processingTime?: number;
  error?: string;
}

export interface ProcessingStatus {
  id: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processedImageUrl?: string;
  processingTime?: number;
  error?: string;
}
